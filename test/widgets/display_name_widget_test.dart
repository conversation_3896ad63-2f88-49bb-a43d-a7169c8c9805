import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gameflex/widgets/display_name_widget.dart';
import 'package:gameflex/theme/app_theme.dart';

void main() {
  group('DisplayNameWidget', () {
    testWidgets('displays username without icon for GameFlex accounts', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DisplayNameWidget(
              displayName: 'TestUser123',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('TestUser123'), findsOneWidget);
      expect(find.byType(SvgPicture), findsNothing);
    });

    testWidgets('displays username with Xbox icon for Xbox accounts', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DisplayNameWidget(
              displayName: '[xbox]GamerTag123',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('GamerTag123'), findsOneWidget);
      expect(find.byType(SvgPicture), findsOneWidget);
    });

    testWidgets('displays username with Twitch icon for Twitch accounts', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DisplayNameWidget(
              displayName: '[twitch]StreamerName',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('StreamerName'), findsOneWidget);
      expect(find.byType(SvgPicture), findsOneWidget);
    });

    testWidgets('displays username with PlayStation icon for PlayStation accounts', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DisplayNameWidget(
              displayName: '[playstation]PSNUser',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('PSNUser'), findsOneWidget);
      expect(find.byType(SvgPicture), findsOneWidget);
    });

    testWidgets('displays username with Kick icon for Kick accounts', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DisplayNameWidget(
              displayName: '[kick]KickStreamer',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('KickStreamer'), findsOneWidget);
      expect(find.byType(SvgPicture), findsOneWidget);
    });

    testWidgets('displays username with material icon for Steam accounts', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DisplayNameWidget(
              displayName: '[steam]SteamUser',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('SteamUser'), findsOneWidget);
      expect(find.byIcon(Icons.computer), findsOneWidget);
    });

    testWidgets('displays username with material icon for Discord accounts', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DisplayNameWidget(
              displayName: '[discord]DiscordUser#1234',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('DiscordUser#1234'), findsOneWidget);
      expect(find.byIcon(Icons.chat), findsOneWidget);
    });

    testWidgets('CompactDisplayNameWidget uses smaller icon size', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactDisplayNameWidget(
              displayName: '[xbox]CompactUser',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('CompactUser'), findsOneWidget);
      expect(find.byType(SvgPicture), findsOneWidget);
    });

    testWidgets('LargeDisplayNameWidget uses larger icon size', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LargeDisplayNameWidget(
              displayName: '[twitch]LargeUser',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('LargeUser'), findsOneWidget);
      expect(find.byType(SvgPicture), findsOneWidget);
    });

    testWidgets('handles unknown providers gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DisplayNameWidget(
              displayName: '[unknown]UnknownUser',
              textStyle: TextStyle(color: AppColors.gfOffWhite),
            ),
          ),
        ),
      );

      expect(find.text('UnknownUser'), findsOneWidget);
      expect(find.byIcon(Icons.help_outline), findsOneWidget);
    });
  });
}
