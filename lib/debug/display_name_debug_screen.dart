import 'package:flutter/material.dart';
import '../widgets/display_name_widget.dart';
import '../theme/app_theme.dart';

/// Debug screen to test DisplayNameWidget with various inputs
class DisplayNameDebugScreen extends StatelessWidget {
  const DisplayNameDebugScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground100,
      appBar: AppBar(
        title: const Text('Display Name Debug'),
        backgroundColor: AppColors.gfDarkBackground100,
        foregroundColor: AppColors.gfOffWhite,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Testing DisplayNameWidget with various inputs:',
              style: TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            _buildTestCase('GameFlex User', 'TestUser123'),
            _buildTestCase('Xbox User', '[xbox]GamerTag123'),
            _buildTestCase('Twitch User', '[twitch]StreamerName'),
            _buildTestCase('PlayStation User', '[playstation]PSNUser'),
            _buildTestCase('Kick User', '[kick]KickStreamer'),
            _buildTestCase('Steam User', '[steam]SteamUser'),
            _buildTestCase('Discord User', '[discord]DiscordUser#1234'),
            _buildTestCase('Apple User', '[apple]AppleUser'),
            _buildTestCase('Google User', '[google]GoogleUser'),
            _buildTestCase('Unknown Provider', '[unknown]UnknownUser'),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCase(String label, String displayName) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: "$displayName"',
            style: const TextStyle(
              color: AppColors.gfGrayText,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.gfCardBackground,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Text(
                  'Normal: ',
                  style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
                ),
                DisplayNameWidget(
                  displayName: displayName,
                  textStyle: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 20),
                const Text(
                  'Large: ',
                  style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
                ),
                LargeDisplayNameWidget(
                  displayName: displayName,
                  textStyle: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
