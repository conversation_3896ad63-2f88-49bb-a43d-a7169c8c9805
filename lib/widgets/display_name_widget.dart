import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../utils/display_name_utils.dart';
import '../config/display_name_config.dart';
import '../theme/app_theme.dart';

/// A widget that displays a formatted display name with the appropriate provider icon
///
/// This widget parses display names like '[twitch]Username123' and shows:
/// - The provider's SVG icon (from assets/images/icons/third_party_auth/)
/// - The username without brackets
///
/// For GameFlex native accounts (no brackets), only the username is shown.
class DisplayNameWidget extends StatelessWidget {
  final String displayName;
  final TextStyle? textStyle;
  final double iconSize;
  final double iconSpacing;
  final bool showIcon;
  final TextOverflow? overflow;
  final int? maxLines;

  const DisplayNameWidget({
    super.key,
    required this.displayName,
    this.textStyle,
    this.iconSize = 16,
    this.iconSpacing = 6,
    this.showIcon = true,
    this.overflow,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    final parsed = parseDisplayName(displayName);
    final provider = parsed['provider']!;
    final username = parsed['displayName']!;

    // If it's a GameFlex account or showIcon is false, just show the username
    if (!showIcon || provider == DisplayNameConfig.defaultProvider) {
      return Text(
        username,
        style: textStyle,
        overflow: overflow,
        maxLines: maxLines,
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildProviderIcon(provider),
        SizedBox(width: iconSpacing),
        Flexible(
          child: Text(
            username,
            style: textStyle,
            overflow: overflow,
            maxLines: maxLines,
          ),
        ),
      ],
    );
  }

  Widget _buildProviderIcon(String provider) {
    // Map provider strings to SVG file names
    String? svgAsset;
    Color? iconColor;

    try {
      final providerEnum = DisplayNameProvider.fromString(provider);
      iconColor = Color(providerEnum.primaryColor);

      switch (providerEnum) {
        case DisplayNameProvider.xbox:
          svgAsset = 'assets/images/icons/third_party_auth/xbox.svg';
          break;
        case DisplayNameProvider.playstation:
          svgAsset =
              'assets/images/icons/third_party_auth/sony-playstation.svg';
          break;
        case DisplayNameProvider.twitch:
          svgAsset = 'assets/images/icons/third_party_auth/twitch.svg';
          break;
        case DisplayNameProvider.kick:
          svgAsset = 'assets/images/icons/third_party_auth/kick.svg';
          break;
        case DisplayNameProvider.steam:
          // Note: No steam.svg found in the directory, fallback to material icon
          return Icon(Icons.computer, size: iconSize, color: iconColor);
        case DisplayNameProvider.discord:
          // Note: No discord.svg found in the directory, fallback to material icon
          return Icon(Icons.chat, size: iconSize, color: iconColor);
        case DisplayNameProvider.gf:
          return Icon(
            Icons.person,
            size: iconSize,
            color: AppColors.gfGrayText,
          );
      }
    } catch (e) {
      // Handle additional providers that might not be in the enum but have SVG files
      switch (provider.toLowerCase()) {
        case 'apple':
          svgAsset = 'assets/images/icons/third_party_auth/apple.svg';
          iconColor = Colors.white; // Apple typically uses white/black
          break;
        case 'google':
          svgAsset = 'assets/images/icons/third_party_auth/google.svg';
          iconColor = null; // Google logo should keep original colors
          break;
        default:
          // Fallback for unknown providers
          return Icon(
            Icons.help_outline,
            size: iconSize,
            color: AppColors.gfGrayText,
          );
      }
    }

    return SvgPicture.asset(
      svgAsset,
      width: iconSize,
      height: iconSize,
      colorFilter:
          iconColor != null
              ? ColorFilter.mode(iconColor, BlendMode.srcIn)
              : null,
    );
  }
}

/// A compact version of DisplayNameWidget for smaller spaces
class CompactDisplayNameWidget extends StatelessWidget {
  final String displayName;
  final TextStyle? textStyle;
  final TextOverflow? overflow;
  final int? maxLines;

  const CompactDisplayNameWidget({
    super.key,
    required this.displayName,
    this.textStyle,
    this.overflow,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    return DisplayNameWidget(
      displayName: displayName,
      textStyle: textStyle,
      iconSize: 12,
      iconSpacing: 4,
      overflow: overflow,
      maxLines: maxLines,
    );
  }
}

/// A large version of DisplayNameWidget for profile headers
class LargeDisplayNameWidget extends StatelessWidget {
  final String displayName;
  final TextStyle? textStyle;
  final TextOverflow? overflow;
  final int? maxLines;

  const LargeDisplayNameWidget({
    super.key,
    required this.displayName,
    this.textStyle,
    this.overflow,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    return DisplayNameWidget(
      displayName: displayName,
      textStyle: textStyle,
      iconSize: 20,
      iconSpacing: 8,
      showIcon: true, // Explicitly enable icons for large display names
      overflow: overflow,
      maxLines: maxLines,
    );
  }
}
