import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../theme/design_config.dart';
import '../../models/post_model.dart';
import '../core/gf_card.dart';
import '../core/gf_avatar.dart';
import '../media/gf_image.dart';
import '../media/gf_video_player.dart';

import 'gf_reaction_bar.dart';

/// GameFlex post card component
///
/// A reusable post card component that displays post content with consistent
/// styling and interaction patterns across the app.
///
/// Example usage:
/// ```dart
/// GFPostCard(
///   post: post,
///   onTap: () => _viewPost(post),
///   onReaction: (emoji) => _addReaction(post, emoji),
///   showFullContent: false,
/// )
/// ```
class GFPostCard extends StatelessWidget {
  final PostModel post;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Function(String)? onReaction;
  final VoidCallback? onShowReactionPicker;
  final VoidCallback? onShowReactionDetails;
  final VoidCallback? onUserTap;
  final bool showFullContent;
  final EdgeInsetsGeometry? margin;

  const GFPostCard({
    super.key,
    required this.post,
    this.onTap,
    this.onLongPress,
    this.onReaction,
    this.onShowReactionPicker,
    this.onShowReactionDetails,
    this.onUserTap,
    this.showFullContent = false,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      margin: margin,
      onTap: onTap,
      onLongPress: onLongPress,
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info header
          _buildUserHeader(),

          // Media content
          _buildMediaContent(),

          // Post content and interactions
          _buildPostContent(),
        ],
      ),
    );
  }

  Widget _buildUserHeader() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: [
          GFAvatar(
            imageUrl: post.avatarUrl,
            displayName: post.authorDisplayName,
            radius: 20,
            onTap: onUserTap,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              post.authorDisplayName,
              style: const TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          // Timestamp
          Text(
            _formatTimestamp(post.createdAt),
            style: const TextStyle(color: AppColors.gfGrayText, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaContent() {
    if (post.isImage && post.mediaUrl != null && post.mediaUrl!.isNotEmpty) {
      return GFImage(
        imageUrl: post.mediaUrl!,
        aspectRatio: 16 / 9,
        fit: BoxFit.cover,
        onTap: onTap,
      );
    } else if (post.isVideo &&
        post.mediaUrl != null &&
        post.mediaUrl!.isNotEmpty) {
      return GFVideoPlayer(
        videoUrl: post.mediaUrl!,
        aspectRatio: 16 / 9,
        autoPlay: false,
        showControls: true,
        onTap: onTap,
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildPostContent() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post content
          if (post.content.isNotEmpty) ...[
            Text(
              post.content,
              style: const TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 14,
                height: 1.4,
              ),
              maxLines: showFullContent ? null : 3,
              overflow: showFullContent ? null : TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),
          ],

          // Channel info
          if (post.channelName != null && post.channelName!.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '#${post.channelName}',
                style: const TextStyle(
                  color: AppColors.gfGreen,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Reaction bar
          if (post.reactions.isNotEmpty || onShowReactionPicker != null)
            GFReactionBar(
              reactions: post.reactions,
              currentUserReaction: post.currentUserReaction,
              onReact: onReaction ?? (_) {},
              onShowReactionPicker: onShowReactionPicker,
              onShowReactionDetails: onShowReactionDetails,
            ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}

/// Compact post card for lists
class GFCompactPostCard extends StatelessWidget {
  final PostModel post;
  final VoidCallback? onTap;
  final VoidCallback? onUserTap;

  const GFCompactPostCard({
    super.key,
    required this.post,
    this.onTap,
    this.onUserTap,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      onTap: onTap,
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // Media thumbnail
          _buildMediaThumbnail(),
          const SizedBox(width: 12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User info
                Row(
                  children: [
                    GFAvatar(
                      imageUrl: post.avatarUrl,
                      displayName: post.authorDisplayName,
                      radius: 12,
                      onTap: onUserTap,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        post.authorDisplayName,
                        style: const TextStyle(
                          color: AppColors.gfOffWhite,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(
                      _formatTimestamp(post.createdAt),
                      style: const TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),

                // Content
                if (post.content.isNotEmpty)
                  Text(
                    post.content,
                    style: const TextStyle(
                      color: AppColors.gfGrayText,
                      fontSize: 13,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaThumbnail() {
    if (post.isImage && post.mediaUrl != null && post.mediaUrl!.isNotEmpty) {
      return GFImage(
        imageUrl: post.mediaUrl!,
        width: 60,
        height: 60,
        fit: BoxFit.cover,
        borderRadius: BorderRadius.circular(8),
      );
    } else if (post.isVideo &&
        post.mediaUrl != null &&
        post.mediaUrl!.isNotEmpty) {
      return Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: DesignConfig.cardBackground,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(
          Icons.play_circle_outline,
          color: DesignConfig.primaryColor,
          size: 24,
        ),
      );
    }

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.article_outlined,
        color: AppColors.gfGrayText,
        size: 24,
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}
