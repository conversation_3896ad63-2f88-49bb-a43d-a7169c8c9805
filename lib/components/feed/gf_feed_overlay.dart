import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../theme/app_theme.dart';
import '../../models/post_model.dart';
import '../core/gf_avatar.dart';
import '../layout/gf_overlay.dart';
import 'gf_reaction_bar.dart';
import '../../providers/posts_provider.dart';
import '../../services/aws_posts_service.dart';
import '../../screens/post_detail_screen.dart';
import '../../utils/app_logger.dart';
import '../../screens/reflex_creation_screen.dart';
import '../forms/gf_emoji_picker.dart';
import '../../services/engagement_tracking_service.dart';
import '../../widgets/display_name_widget.dart';

/// GameFlex feed overlay components
///
/// Components for overlaying information on top of feed content,
/// such as user info, action buttons, and interaction elements.

/// Top overlay for feed items showing user information
class GFFeedTopOverlay extends StatelessWidget {
  final PostModel post;
  final VoidCallback? onUserTap;
  final VoidCallback? onChannelTap;
  final Widget? trailing;

  const GFFeedTopOverlay({
    super.key,
    required this.post,
    this.onUserTap,
    this.onChannelTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return GFTopOverlay(
      child: GestureDetector(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info row with avatar back in top overlay
            Row(
              children: [
                // Avatar with same pattern as working action buttons
                GestureDetector(
                  onTap: () {
                    AppLogger.debug('Avatar tapped in top overlay');
                    onUserTap?.call();
                  },
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(
                        alpha: 102,
                      ), // Debug background
                      shape: BoxShape.circle,
                    ),
                    child: GFAvatar(
                      imageUrl: post.avatarUrl,
                      displayName: post.authorDisplayName,
                      radius: 20,
                      showBorder: true,
                      borderColor: Colors.white.withValues(alpha: 128),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DisplayNameWidget(
                        displayName: post.authorDisplayName,
                        textStyle: const TextStyle(
                          color: AppColors.gfGreen,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          shadows: [
                            Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 2,
                              color: Colors.black,
                            ),
                          ],
                        ),
                      ),
                      if (post.channelName != null &&
                          post.channelName!.isNotEmpty)
                        GestureDetector(
                          onTap: onChannelTap,
                          child: Container(
                            margin: const EdgeInsets.only(top: 4),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.gfGreen.withValues(
                                alpha: 179,
                              ), // 0.7 opacity
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '#${post.channelName}',
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                if (trailing != null) trailing!,
              ],
            ),

            // Post content
            if (post.content.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                post.content,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  height: 1.4,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 2,
                      color: Colors.black,
                    ),
                  ],
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Bottom overlay for feed items showing actions and interactions
class GFFeedBottomOverlay extends StatefulWidget {
  final PostModel post;
  final Function(String)? onReaction;
  final VoidCallback? onShowReactionPicker;
  final VoidCallback? onShowReactionDetails;
  final VoidCallback? onComment;
  final VoidCallback? onShare;
  final VoidCallback? onMore;
  final VoidCallback? onUserTap; // Add onUserTap parameter
  final List<Widget>? customActions;

  const GFFeedBottomOverlay({
    super.key,
    required this.post,
    this.onReaction,
    this.onShowReactionPicker,
    this.onShowReactionDetails,
    this.onComment,
    this.onShare,
    this.onMore,
    this.onUserTap, // Add onUserTap parameter
    this.customActions,
  });

  @override
  State<GFFeedBottomOverlay> createState() => _GFFeedBottomOverlayState();
}

class _GFFeedBottomOverlayState extends State<GFFeedBottomOverlay>
    with TickerProviderStateMixin {
  late AnimationController _reactionAnimationController;
  late Animation<double> _reactionScaleAnimation;
  bool _isReacting = false;

  @override
  void initState() {
    super.initState();
    _reactionAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _reactionScaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(
        parent: _reactionAnimationController,
        curve: Curves.elasticOut,
      ),
    );
  }

  @override
  void dispose() {
    _reactionAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GFBottomOverlay(
      child: GestureDetector(
        onLongPress: () => _showEmojiPicker(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Reactions - always show, with add button when empty
            AnimatedBuilder(
              animation: _reactionScaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _isReacting ? _reactionScaleAnimation.value : 1.0,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: GFReactionBar(
                      reactions: widget.post.reactions,
                      currentUserReaction: widget.post.currentUserReaction,
                      onReact: (emoji) => _handleReact(context, emoji),
                      onShowReactionPicker: () => _showEmojiPicker(context),
                    ),
                  ),
                );
              },
            ),

            // Action buttons row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Left side - More options
                _buildActionButton(
                  icon: Icons.more_vert,
                  label: '',
                  color: Colors.white,
                  onPressed: () => _showPostOptions(context),
                ),

                // Right side - Comments, Likes, Reflex
                Row(
                  children: [
                    _buildActionButton(
                      icon: Icons.chat_bubble_outline,
                      label: widget.post.commentCount.toString(),
                      color: Colors.white,
                      onPressed: () => _handleComment(context),
                    ),
                    const SizedBox(width: 16),
                    _buildActionButton(
                      icon:
                          widget.post.isLikedByCurrentUser
                              ? Icons.shield
                              : Icons.shield_outlined,
                      label: widget.post.likeCount.toString(),
                      color:
                          widget.post.isLikedByCurrentUser
                              ? AppColors.gfGreen
                              : Colors.white,
                      onPressed: () => _handleLike(context),
                    ),
                    const SizedBox(width: 16),
                    _buildActionButton(
                      icon: Icons.sports_martial_arts,
                      label: widget.post.reflexCount.toString(),
                      color: Colors.white,
                      onPressed: () => _handleReflex(context),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color.fromARGB(200, 96, 96, 96),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          if (label.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w800,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 2,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _handleLike(BuildContext context) async {
    // Track engagement interaction
    EngagementTrackingService.instance.markInteraction(widget.post.id, 'like');

    final postsProvider = Provider.of<PostsProvider>(context, listen: false);
    await postsProvider.toggleLike(widget.post.id);
  }

  void _handleComment(BuildContext context) {
    // Track engagement interaction
    EngagementTrackingService.instance.markInteraction(
      widget.post.id,
      'comment',
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: widget.post),
      ),
    );
  }

  void _handleReflex(BuildContext context) {
    // Track engagement interaction
    EngagementTrackingService.instance.markInteraction(
      widget.post.id,
      'reflex',
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ReflexCreationScreen(originalPost: widget.post),
      ),
    );
  }

  void _handleReact(BuildContext context, String emoji) async {
    // Track engagement interaction
    EngagementTrackingService.instance.markInteraction(
      widget.post.id,
      'reaction',
    );

    // Add visual feedback
    setState(() {
      _isReacting = true;
    });
    _reactionAnimationController.forward().then((_) {
      _reactionAnimationController.reverse();
      setState(() {
        _isReacting = false;
      });
    });

    final postsProvider = Provider.of<PostsProvider>(context, listen: false);
    final idx = postsProvider.posts.indexWhere((p) => p.id == widget.post.id);

    if (idx != -1) {
      final old = postsProvider.posts[idx];
      final newReactions = Map<String, int>.from(old.reactions);

      // Remove current reaction if exists
      if (old.currentUserReaction != null) {
        final currentEmoji = old.currentUserReaction!;
        final currentCount = newReactions[currentEmoji] ?? 0;
        if (currentCount > 1) {
          newReactions[currentEmoji] = currentCount - 1;
        } else {
          newReactions.remove(currentEmoji);
        }
      }

      // Add new reaction (or remove if same emoji)
      String? newUserReaction;
      if (emoji.isNotEmpty && emoji != old.currentUserReaction) {
        newReactions[emoji] = (newReactions[emoji] ?? 0) + 1;
        newUserReaction = emoji;
      }

      postsProvider.updatePost(
        old.copyWith(
          reactions: newReactions,
          currentUserReaction: newUserReaction,
        ),
      );
    }

    // Make API call
    final ok =
        emoji.isEmpty || emoji == widget.post.currentUserReaction
            ? await AwsPostsService.instance.unreactToPost(
              widget.post.id,
              widget.post.currentUserReaction ?? '',
            )
            : await AwsPostsService.instance.reactToPost(widget.post.id, emoji);

    if (!ok && context.mounted) {
      postsProvider.refreshPost(widget.post.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to update reaction'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showEmojiPicker(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => GFEmojiPickerDialog(
            onEmojiSelected: (emoji) => _handleReact(context, emoji),
            title: 'React to post',
          ),
    );
  }

  void _showPostOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.gfDarkBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.share, color: AppColors.gfOffWhite),
                  title: const Text(
                    'Share Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _handleShare(context);
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.bookmark_border,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Save Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Save feature coming soon!'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.report_outlined,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Report Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Report feature coming soon!'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _handleShare(BuildContext context) async {
    try {
      // Build the share content
      final post = widget.post;
      final author = post.authorDisplayName;
      final content = post.content;

      // Create share text
      String shareText = 'Check out this post by $author on GameFlex!\n\n';
      if (content.isNotEmpty) {
        shareText += '"$content"\n\n';
      }

      // Add channel info if available
      if (post.channelName != null && post.channelName!.isNotEmpty) {
        shareText += 'Posted in #${post.channelName}\n\n';
      }

      // Add app promotion
      shareText += 'Join the gaming community on GameFlex!';

      // Share the post content
      await Share.share(shareText, subject: 'GameFlex Post by $author');

      AppLogger.debug('Post shared successfully: ${post.id}');
    } catch (e) {
      AppLogger.error('Error sharing post', error: e);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to share post'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Side overlay for additional actions (like reflexes)
class GFFeedSideOverlay extends StatelessWidget {
  final List<GFFeedSideAction> actions;
  final bool isRight;

  const GFFeedSideOverlay({
    super.key,
    required this.actions,
    this.isRight = true,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      bottom: 0,
      right: isRight ? 16 : null,
      left: isRight ? null : 16,
      child: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: actions.map((action) => _buildActionItem(action)).toList(),
        ),
      ),
    );
  }

  Widget _buildActionItem(GFFeedSideAction action) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        children: [
          GestureDetector(
            onTap: action.onTap,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 128), // 0.5 opacity
                shape: BoxShape.circle,
              ),
              child: Icon(action.icon, color: Colors.white, size: 24),
            ),
          ),
          if (action.label != null) ...[
            const SizedBox(height: 4),
            Text(
              action.label!,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
                shadows: [
                  Shadow(
                    offset: Offset(0, 1),
                    blurRadius: 2,
                    color: Colors.black,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Configuration for side overlay actions
class GFFeedSideAction {
  final IconData icon;
  final String? label;
  final VoidCallback onTap;

  const GFFeedSideAction({required this.icon, this.label, required this.onTap});
}

/// Floating overlay for temporary messages or notifications
class GFFeedFloatingMessage extends StatelessWidget {
  final String message;
  final IconData? icon;
  final Color? backgroundColor;
  final Duration duration;
  final VoidCallback? onDismiss;

  const GFFeedFloatingMessage({
    super.key,
    required this.message,
    this.icon,
    this.backgroundColor,
    this.duration = const Duration(seconds: 3),
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    // Auto-dismiss after duration
    Future.delayed(duration, () {
      if (onDismiss != null) {
        onDismiss!();
      }
    });

    return GFFloatingOverlay(
      alignment: Alignment.topCenter,
      margin: const EdgeInsets.only(top: 100, left: 16, right: 16),
      backgroundColor: backgroundColor ?? AppColors.gfDarkBackground,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, color: AppColors.gfOffWhite, size: 20),
              const SizedBox(width: 8),
            ],
            Flexible(
              child: Text(
                message,
                style: const TextStyle(
                  color: AppColors.gfOffWhite,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
