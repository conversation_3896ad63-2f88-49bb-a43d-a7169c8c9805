# Lambda Functions Module for GameFlex Backend

locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  # Lambda function configurations
  lambda_functions = {
    auth = {
      source_dir = "${path.root}/../src/auth"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    auth_signup = {
      source_dir = "${path.root}/../src/auth"
      handler    = "signup.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    auth_set_username = {
      source_dir = "${path.root}/../src/auth"
      handler    = "set-username.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    auth_forgot_password = {
      source_dir = "${path.root}/../src/auth"
      handler    = "forgot-password.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    auth_reset_password = {
      source_dir = "${path.root}/../src/auth"
      handler    = "confirm-forgot-password.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    auth_confirm = {
      source_dir = "${path.root}/../src/auth"
      handler    = "confirm-signup.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    authorizer = {
      source_dir = "${path.root}/../src/authorizer"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    channels = {
      source_dir = "${path.root}/../src/channels"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    health = {
      source_dir = "${path.root}/../src/health"
      handler    = "index.handler"
      timeout    = 10
      memory     = 512  # Increased from 128MB for better cold start performance
    }
    media = {
      source_dir = "${path.root}/../src/media"
      handler    = "index.handler"
      timeout    = 60
      memory     = var.memory_size * 2
    }
    posts = {
      source_dir = "${path.root}/../src/posts"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    reflexes = {
      source_dir = "${path.root}/../src/reflexes"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    users = {
      source_dir = "${path.root}/../src/users"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    notifications = {
      source_dir = "${path.root}/../src/notifications"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    ai_processing = {
      source_dir = "${path.root}/../src/ai-processing"
      handler    = "index.handler"
      timeout    = 300
      memory     = 1024
    }
    media_processor = {
      source_dir = "${path.root}/../src/media-processor"
      handler    = "index.handler"
      timeout    = 300
      memory     = 1024
    }
    xbox = {
      source_dir = "${path.root}/../src/xbox"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    xbox-media = {
      source_dir = "${path.root}/../src/xbox"
      handler    = "media.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    twitch = {
      source_dir = "${path.root}/../src/twitch"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
    kick = {
      source_dir = "${path.root}/../src/kick"
      handler    = "index.handler"
      timeout    = 30
      memory     = var.memory_size
    }
  }
  
  # Common environment variables for all Lambda functions
  common_env_vars = {
    ENVIRONMENT              = var.environment
    PROJECT_NAME            = var.project_name
    USER_POOL_ID            = var.user_pool.id
    USER_POOL_CLIENT_ID     = var.user_pool_client.id
    MEDIA_BUCKET            = var.media_bucket.bucket
    MEDIA_BUCKET_NAME       = var.media_bucket.bucket
    MEDIA_DOMAIN_NAME       = var.media_domain_name
    DISTRIBUTION_DOMAIN     = var.distribution.domain_name
    CLOUDFRONT_DOMAIN       = var.media_domain_name != "" ? var.media_domain_name : var.distribution.domain_name

    # Secret names (with _NAME suffix as expected by Lambda functions)
    APP_CONFIG_SECRET_NAME  = var.app_config_secret
    APPLE_CONFIG_SECRET_NAME = var.apple_config_secret
    XBOX_CONFIG_SECRET_NAME = var.xbox_config_secret
    TWITCH_CONFIG_SECRET_NAME = var.twitch_config_secret
    KICK_CONFIG_SECRET_NAME = var.kick_config_secret
    JWT_SECRET_NAME         = var.jwt_secret
    
    # DynamoDB table names (with _NAME suffix for compatibility)
    POSTS_TABLE_NAME         = var.database_tables.posts.name
    MEDIA_TABLE_NAME         = var.database_tables.media.name
    USER_PROFILES_TABLE_NAME = var.database_tables.user_profiles.name
    USERS_TABLE_NAME         = var.database_tables.users.name
    LIKES_TABLE_NAME         = var.database_tables.likes.name
    FOLLOWS_TABLE_NAME       = var.database_tables.follows.name
    CHANNELS_TABLE_NAME      = var.database_tables.channels.name
    CHANNEL_MEMBERS_TABLE_NAME = var.database_tables.channel_members.name
    REFLEXES_TABLE_NAME      = var.database_tables.reflexes.name
    REFLEX_LIKES_TABLE_NAME  = var.database_tables.reflex_likes.name
    REFLEX_REACTIONS_TABLE_NAME = var.database_tables.reflex_reactions.name
    COMMENTS_TABLE_NAME      = var.database_tables.comments.name
    XBOX_ACCOUNTS_TABLE_NAME = var.database_tables.xbox_accounts.name
    POST_VIEWS_TABLE_NAME    = var.database_tables.post_views.name
    POST_REACTIONS_TABLE_NAME = var.database_tables.post_reactions.name
    POST_ENGAGEMENT_METRICS_TABLE_NAME = var.database_tables.post_engagement_metrics.name
    USER_PREFERENCES_TABLE_NAME = var.database_tables.user_preferences.name
    USER_HISTORY_TABLE_NAME     = var.database_tables.user_history.name
    USERAUTHS_TABLE_NAME        = var.database_tables.user_auths.name

    # DynamoDB table names (without _NAME suffix for Lambda functions)
    POSTS_TABLE              = var.database_tables.posts.name
    MEDIA_TABLE              = var.database_tables.media.name
    USER_PROFILES_TABLE      = var.database_tables.user_profiles.name
    USERS_TABLE              = var.database_tables.users.name
    LIKES_TABLE              = var.database_tables.likes.name
    FOLLOWS_TABLE            = var.database_tables.follows.name
    CHANNELS_TABLE           = var.database_tables.channels.name
    REFLEXES_TABLE           = var.database_tables.reflexes.name
    REFLEX_LIKES_TABLE       = var.database_tables.reflex_likes.name
    REFLEX_REACTIONS_TABLE   = var.database_tables.reflex_reactions.name
    COMMENTS_TABLE           = var.database_tables.comments.name
    XBOX_ACCOUNTS_TABLE      = var.database_tables.xbox_accounts.name
    POST_VIEWS_TABLE         = var.database_tables.post_views.name
    POST_REACTIONS_TABLE     = var.database_tables.post_reactions.name
    POST_ENGAGEMENT_METRICS_TABLE = var.database_tables.post_engagement_metrics.name
    USER_PREFERENCES_TABLE   = var.database_tables.user_preferences.name
    USER_HISTORY_TABLE       = var.database_tables.user_history.name
    USER_AUTHS_TABLE         = var.database_tables.user_auths.name

    # Notification-related tables
    NOTIFICATIONS_TABLE             = var.notification_stack.table_names.notifications
    DEVICE_TOKENS_TABLE            = var.notification_stack.table_names.device_tokens
    NOTIFICATION_PREFERENCES_TABLE = var.notification_stack.table_names.notification_preferences
    NOTIFICATION_HISTORY_TABLE     = var.notification_stack.table_names.notification_history

    # SNS Topic
    SNS_TOPIC_ARN = var.notification_stack.topic_arn

    # Additional table names for posts Lambda function
    CHANNEL_MEMBERS_TABLE    = var.database_tables.channel_members.name
    POST_REACTIONS_TABLE     = var.database_tables.post_reactions.name
    POST_VIEWS_TABLE         = var.database_tables.post_views.name
    POST_ENGAGEMENT_METRICS_TABLE = var.database_tables.post_engagement_metrics.name
    USER_PREFERENCES_TABLE   = var.database_tables.user_preferences.name
    USER_HISTORY_TABLE       = var.database_tables.user_history.name

    # S3 bucket name
    MEDIA_BUCKET             = var.media_bucket.bucket
    
    # Notification resources
    NOTIFICATION_TOPIC_ARN = var.notification_stack.topic_arn
    NOTIFICATIONS_TABLE_NAME = var.notification_stack.table_names.notifications
    DEVICE_TOKENS_TABLE_NAME = var.notification_stack.table_names.device_tokens
    NOTIFICATION_PREFERENCES_TABLE_NAME = var.notification_stack.table_names.notification_preferences
    NOTIFICATION_HISTORY_TABLE_NAME = var.notification_stack.table_names.notification_history

    # Notification table names (without _NAME suffix for Lambda functions)
    NOTIFICATIONS_TABLE = var.notification_stack.table_names.notifications
    DEVICE_TOKENS_TABLE = var.notification_stack.table_names.device_tokens
    NOTIFICATION_PREFERENCES_TABLE = var.notification_stack.table_names.notification_preferences
    NOTIFICATION_HISTORY_TABLE = var.notification_stack.table_names.notification_history
    SNS_TOPIC_ARN = var.notification_stack.topic_arn
  }
}

# IAM Role for Lambda Functions
resource "aws_iam_role" "lambda_execution_role" {
  name = "${local.name_prefix}-lambda-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Basic Lambda execution policy
resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Custom IAM policy for Lambda functions
resource "aws_iam_role_policy" "lambda_custom_policy" {
  name = "${local.name_prefix}-lambda-custom-policy"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      # DynamoDB permissions
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan",
          "dynamodb:BatchGetItem",
          "dynamodb:BatchWriteItem"
        ]
        Resource = [
          var.database_tables.posts.arn,
          var.database_tables.media.arn,
          var.database_tables.user_profiles.arn,
          var.database_tables.users.arn,
          var.database_tables.likes.arn,
          var.database_tables.follows.arn,
          var.database_tables.channels.arn,
          var.database_tables.channel_members.arn,
          var.database_tables.reflexes.arn,
          var.database_tables.reflex_likes.arn,
          var.database_tables.reflex_reactions.arn,
          var.database_tables.comments.arn,
          var.database_tables.xbox_accounts.arn,
          var.database_tables.post_views.arn,
          var.database_tables.post_reactions.arn,
          var.database_tables.post_engagement_metrics.arn,
          var.database_tables.user_preferences.arn,
          var.database_tables.user_history.arn,
          var.database_tables.user_auths.arn,
          var.notification_stack.table_arns.notifications,
          var.notification_stack.table_arns.device_tokens,
          var.notification_stack.table_arns.notification_preferences,
          var.notification_stack.table_arns.notification_history,
          "${var.database_tables.posts.arn}/index/*",
          "${var.database_tables.media.arn}/index/*",
          "${var.database_tables.user_profiles.arn}/index/*",
          "${var.database_tables.users.arn}/index/*",
          "${var.database_tables.likes.arn}/index/*",
          "${var.database_tables.follows.arn}/index/*",
          "${var.database_tables.channels.arn}/index/*",
          "${var.database_tables.reflexes.arn}/index/*",
          "${var.database_tables.reflex_likes.arn}/index/*",
          "${var.database_tables.reflex_reactions.arn}/index/*",
          "${var.database_tables.comments.arn}/index/*",
          "${var.database_tables.xbox_accounts.arn}/index/*",
          "${var.database_tables.post_views.arn}/index/*",
          "${var.database_tables.post_reactions.arn}/index/*",
          "${var.database_tables.post_engagement_metrics.arn}/index/*",
          "${var.database_tables.user_preferences.arn}/index/*",
          "${var.database_tables.user_history.arn}/index/*",
          "${var.database_tables.user_auths.arn}/index/*",
          "${var.notification_stack.table_arns.notifications}/index/*",
          "${var.notification_stack.table_arns.device_tokens}/index/*",
          "${var.notification_stack.table_arns.notification_history}/index/*"
        ]
      },
      # S3 permissions
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:GetObjectVersion",
          "s3:PutObjectAcl"
        ]
        Resource = [
          "${var.media_bucket.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:ListBucket"
        ]
        Resource = var.media_bucket.arn
      },
      # Cognito permissions
      {
        Effect = "Allow"
        Action = [
          "cognito-idp:AdminGetUser",
          "cognito-idp:AdminCreateUser",
          "cognito-idp:AdminUpdateUserAttributes",
          "cognito-idp:AdminDeleteUser",
          "cognito-idp:AdminInitiateAuth",
          "cognito-idp:AdminSetUserPassword",
          "cognito-idp:AdminConfirmSignUp",
          "cognito-idp:ListUsers"
        ]
        Resource = var.user_pool.arn
      },
      # Secrets Manager permissions
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Resource = [
          "arn:aws:secretsmanager:*:*:secret:${var.app_config_secret}*",
          "arn:aws:secretsmanager:*:*:secret:${var.apple_config_secret}*",
          "arn:aws:secretsmanager:*:*:secret:${var.xbox_config_secret}*",
          "arn:aws:secretsmanager:*:*:secret:${var.twitch_config_secret}*",
          "arn:aws:secretsmanager:*:*:secret:${var.kick_config_secret}*",
          "arn:aws:secretsmanager:*:*:secret:${var.jwt_secret}*"
        ]
      },
      # SNS permissions
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = var.notification_stack.topic_arn
      },
      # Rekognition permissions
      {
        Effect = "Allow"
        Action = [
          "rekognition:DetectModerationLabels",
          "rekognition:DetectLabels",
          "rekognition:DetectText"
        ]
        Resource = "*"
      }
    ]
  })
}

# Archive files for Lambda functions
data "archive_file" "lambda_functions" {
  for_each = local.lambda_functions

  type        = "zip"
  output_path = "${path.module}/lambda-${each.key}.zip"
  source_dir  = each.value.source_dir
}

# Lambda functions
resource "aws_lambda_function" "functions" {
  for_each = local.lambda_functions

  filename         = data.archive_file.lambda_functions[each.key].output_path
  function_name    = "${local.name_prefix}-${each.key}"
  role            = aws_iam_role.lambda_execution_role.arn
  handler         = each.value.handler
  runtime         = var.runtime
  timeout         = each.value.timeout
  memory_size     = each.value.memory

  source_code_hash = data.archive_file.lambda_functions[each.key].output_base64sha256

  # Attach the utils layer to all functions
  layers = [var.utils_layer_arn]

  environment {
    variables = local.common_env_vars
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-${each.key}"
    Type = "Lambda Function"
  })
}

# Provisioned Concurrency for critical functions to reduce cold starts
# Note: Disabled for now as it requires published function versions
# resource "aws_lambda_provisioned_concurrency_config" "critical_functions" {
#   for_each = var.is_production_or_staging ? {
#     posts      = aws_lambda_function.functions["posts"]
#     auth       = aws_lambda_function.functions["auth"]
#     authorizer = aws_lambda_function.functions["authorizer"]
#     health     = aws_lambda_function.functions["health"]
#   } : {}
#
#   function_name                     = each.value.function_name
#   provisioned_concurrent_executions = var.environment == "production" ? 5 : 2
#   qualifier                        = "$LATEST"
#
#   depends_on = [aws_lambda_function.functions]
# }

# Reserved Concurrency for development to keep functions warm
resource "aws_lambda_function_event_invoke_config" "critical_functions_config" {
  for_each = {
    posts = aws_lambda_function.functions["posts"]
    auth = aws_lambda_function.functions["auth"]
    authorizer = aws_lambda_function.functions["authorizer"]
    health = aws_lambda_function.functions["health"]
  }

  function_name = each.value.function_name

  # Retry configuration
  maximum_retry_attempts = 1
  maximum_event_age_in_seconds = 60
}

# CloudWatch Log Groups for Lambda functions
resource "aws_cloudwatch_log_group" "lambda_logs" {
  for_each = local.lambda_functions

  name              = "/aws/lambda/${local.name_prefix}-${each.key}"
  retention_in_days = var.log_retention_days

  tags = var.tags
}

# S3 Bucket Notification for AI Processing
resource "aws_s3_bucket_notification" "media_processing" {
  bucket = var.media_bucket.bucket

  lambda_function {
    lambda_function_arn = aws_lambda_function.functions["ai_processing"].arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "staging/"
  }

  depends_on = [aws_lambda_permission.s3_invoke_ai_processing]
}

# Lambda permission for S3 to invoke AI processing function
resource "aws_lambda_permission" "s3_invoke_ai_processing" {
  statement_id  = "AllowExecutionFromS3Bucket"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.functions["ai_processing"].function_name
  principal     = "s3.amazonaws.com"
  source_arn    = var.media_bucket.arn
}
