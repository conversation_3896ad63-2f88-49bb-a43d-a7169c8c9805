# Variables for Authentication Module

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "is_production_or_staging" {
  description = "Whether this is a production or staging environment"
  type        = bool
}

variable "lambda_runtime" {
  description = "Runtime for Lambda functions"
  type        = string
  default     = "nodejs22.x"
}

variable "password_min_length" {
  description = "Minimum password length for Cognito"
  type        = number
  default     = 8
}

variable "token_validity_hours" {
  description = "Token validity in hours for Cognito"
  type        = number
  default     = 1
}

variable "refresh_token_validity_days" {
  description = "Refresh token validity in days for Cognito"
  type        = number
  default     = 30
}

variable "enable_auto_verify_email" {
  description = "Enable automatic email verification in Cognito"
  type        = bool
  default     = true
}

variable "create_user_pool_domain" {
  description = "Whether to create a Cognito User Pool domain"
  type        = bool
  default     = false
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
