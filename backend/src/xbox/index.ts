import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, UpdateCommand, PutCommand, QueryCommand, DeleteCommand } from '@aws-sdk/lib-dynamodb';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
// Removed Cognito imports - Xbox auth now bypasses Cognito
import { v4 as uuidv4 } from 'uuid';
const fetch = require('node-fetch');
import { logXboxSignIn, logXboxSignUp, logAccountLinkedXbox, extractRequestMetadata } from '/opt/nodejs/user-history-service';
import { DisplayNameProvider } from '/opt/nodejs/display-name-config';
import { createResponse, getUserIdFromContext } from '/opt/nodejs/lambda-helpers';
import { UserAuthService } from '/opt/nodejs/user-auth-service';
import { AuthProvider } from '/opt/nodejs/user-auth';
import { generateAuthTokensWithContext } from '/opt/nodejs/jwt-auth-service';

// Xbox provider constant
const XBOX_PROVIDER: DisplayNameProvider = 'xbox';

// AWS Configuration
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const secretsManager = new SecretsManagerClient(awsConfig);
// Removed Cognito client - Xbox auth now bypasses Cognito

const USERS_TABLE = process.env.USERS_TABLE;
const XBOX_ACCOUNTS_TABLE = process.env.XBOX_ACCOUNTS_TABLE;
const USER_AUTHS_TABLE = process.env.USER_AUTHS_TABLE || process.env.USERAUTHS_TABLE_NAME;
const APP_CONFIG_SECRET_NAME = process.env.APP_CONFIG_SECRET_NAME;
const XBOX_CONFIG_SECRET_NAME = process.env.XBOX_CONFIG_SECRET_NAME;
// Removed Cognito environment variables - Xbox auth now bypasses Cognito

// Initialize UserAuth service
const userAuthService = new UserAuthService(USER_AUTHS_TABLE);

// Xbox Live API endpoints
const XBOX_AUTH_URL = 'https://user.auth.xboxlive.com/user/authenticate';
const XSTS_AUTH_URL = 'https://xsts.auth.xboxlive.com/xsts/authorize';
const MICROSOFT_TOKEN_URL = 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token';
const MICROSOFT_AUTH_URL = 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize';

// Xbox Live API endpoints for data
const XBOX_PROFILE_URL = 'https://profile.xboxlive.com/users/me/profile/settings';

let azureConfig: {
    clientId: string;
    clientSecret: string;
} | null = null;

// TypeScript interfaces
interface XboxLinkRequest {
    authCode: string; // OAuth authorization code for linking
}

interface XboxSignInRequest {
    authCode: string; // OAuth authorization code for sign in
}

interface UserRecord {
    id: string;
    email?: string; // Now optional - stored in UserAuths table
    username?: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
    displayNameProvider?: DisplayNameProvider;
    cognitoUserId?: string; // Optional for third-party auth users
    createdAt: string;
    updatedAt: string;
}



interface XboxAccount {
    id: string;
    userId: string;
    xboxUserId: string;
    gamertag: string;
    xstsToken: string;
    userHash: string;
    microsoftRefreshToken?: string; // Microsoft OAuth refresh token for automatic renewal
    profilePictureUrl?: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    tokenExpiresAt: string;
}

// Xbox Live authentication response interfaces
interface XboxLiveAuthResponse {
    IssueInstant: string;
    NotAfter: string;
    Token: string;
    DisplayClaims: {
        xui: Array<{
            uhs: string;
        }>;
    };
}

// Microsoft OAuth token response
interface MicrosoftTokenResponse {
    access_token: string;
    token_type: string;
    expires_in: number;
    scope: string;
    refresh_token?: string;
}



// Helper function to get Azure config from secrets
const getAzureConfig = async (): Promise<typeof azureConfig> => {
    if (azureConfig) {
        return azureConfig;
    }

    // Use Xbox config secret first, then fall back to app config secret
    let secretName = XBOX_CONFIG_SECRET_NAME;
    let useXboxSecret = true;

    if (!secretName) {
        secretName = APP_CONFIG_SECRET_NAME;
        useXboxSecret = false;
    }

    if (!secretName) {
        throw new Error('Neither XBOX_CONFIG_SECRET_NAME nor APP_CONFIG_SECRET_NAME environment variable is set');
    }

    try {
        const command = new GetSecretValueCommand({
            SecretId: secretName,
        });
        const response = await secretsManager.send(command);

        if (!response.SecretString) {
            throw new Error('Secret value is empty');
        }

        const secrets = JSON.parse(response.SecretString);

        azureConfig = {
            clientId: secrets.azureClientId,
            clientSecret: secrets.azureClientSecret
        };

        if (!azureConfig.clientId || !azureConfig.clientSecret) {
            throw new Error(`Azure client ID or secret not found in ${useXboxSecret ? 'Xbox config' : 'app config'} secret`);
        }

        return azureConfig;
    } catch (error) {
        console.error('Error getting Azure config:', error);
        throw new Error(`Failed to retrieve Azure configuration from ${secretName}: ${(error as Error).message}`);
    }
};

// Helper function to refresh Microsoft access token using refresh token
const refreshMicrosoftToken = async (refreshToken: string): Promise<MicrosoftTokenResponse> => {
    try {
        const config = await getAzureConfig();
        if (!config) {
            throw new Error('Azure configuration not available');
        }

        const tokenParams = new URLSearchParams({
            client_id: config.clientId,
            client_secret: config.clientSecret,
            refresh_token: refreshToken,
            grant_type: 'refresh_token',
            scope: 'XboxLive.signin XboxLive.offline_access'
        });

        const response = await fetch(MICROSOFT_TOKEN_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: tokenParams.toString()
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Microsoft token refresh failed: ${response.status} - ${errorText}`);
        }

        const tokenData: MicrosoftTokenResponse = await response.json();
        return tokenData;
    } catch (error) {
        console.error('Error refreshing Microsoft token:', error);
        throw error;
    }
};

// Helper function to refresh Xbox tokens for an existing account
export const refreshXboxTokens = async (xboxAccount: XboxAccount): Promise<{ xstsToken: string; userHash: string; expiresAt: string }> => {
    try {
        if (!xboxAccount.microsoftRefreshToken) {
            throw new Error('No Microsoft refresh token available');
        }

        // Refresh Microsoft access token
        const microsoftTokens = await refreshMicrosoftToken(xboxAccount.microsoftRefreshToken);

        // Get new Xbox Live tokens using the refreshed access token
        const xboxTokens = await getXboxLiveTokens(microsoftTokens.access_token);

        // Update the Xbox account record with new tokens
        const updateParams = {
            TableName: XBOX_ACCOUNTS_TABLE,
            Key: { id: xboxAccount.id },
            UpdateExpression: 'SET xstsToken = :xstsToken, userHash = :userHash, tokenExpiresAt = :tokenExpiresAt, microsoftRefreshToken = :refreshToken, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':xstsToken': xboxTokens.xstsToken,
                ':userHash': xboxTokens.userHash,
                ':tokenExpiresAt': xboxTokens.expiresAt,
                ':refreshToken': microsoftTokens.refresh_token || xboxAccount.microsoftRefreshToken, // Use new refresh token if provided
                ':updatedAt': new Date().toISOString()
            }
        };

        await dynamodb.send(new UpdateCommand(updateParams));

        console.log('Xbox tokens refreshed successfully for account:', xboxAccount.id);
        return xboxTokens;
    } catch (error) {
        console.error('Error refreshing Xbox tokens:', error);
        throw error;
    }
};

// Helper function to exchange Microsoft access token for Xbox Live tokens
const getXboxLiveTokens = async (accessToken: string): Promise<{ xstsToken: string; userHash: string; expiresAt: string }> => {
    try {
        // Step 1: Authenticate with Xbox Live using Microsoft access token
        const xboxAuthResponse = await fetch(XBOX_AUTH_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-xbl-contract-version': '1'
            },
            body: JSON.stringify({
                Properties: {
                    AuthMethod: 'RPS',
                    SiteName: 'user.auth.xboxlive.com',
                    RpsTicket: `d=${accessToken}`
                },
                RelyingParty: 'http://auth.xboxlive.com',
                TokenType: 'JWT'
            })
        });

        if (!xboxAuthResponse.ok) {
            throw new Error(`Xbox Live authentication failed: ${xboxAuthResponse.status}`);
        }

        const xboxAuthData: XboxLiveAuthResponse = await xboxAuthResponse.json();

        // Step 2: Get XSTS token for Xbox Live API access
        const xstsResponse = await fetch(XSTS_AUTH_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-xbl-contract-version': '1'
            },
            body: JSON.stringify({
                Properties: {
                    SandboxId: 'RETAIL',
                    UserTokens: [xboxAuthData.Token]
                },
                RelyingParty: 'http://xboxlive.com',
                TokenType: 'JWT'
            })
        });

        if (!xstsResponse.ok) {
            throw new Error(`XSTS authentication failed: ${xstsResponse.status}`);
        }

        const xstsData: XboxLiveAuthResponse = await xstsResponse.json();

        return {
            xstsToken: xstsData.Token,
            userHash: xstsData.DisplayClaims.xui[0].uhs,
            expiresAt: xstsData.NotAfter
        };
    } catch (error) {
        console.error('Error getting Xbox Live tokens:', error);
        throw error;
    }
};

// Helper function to get Xbox profile using XSTS token
const getXboxProfile = async (xstsToken: string, userHash: string): Promise<any> => {
    try {
        const response = await fetch(`${XBOX_PROFILE_URL}?settings=GameDisplayName,GameDisplayPicRaw,Gamertag`, {
            headers: {
                'Authorization': `XBL3.0 x=${userHash};${xstsToken}`,
                'x-xbl-contract-version': '2'
            }
        });

        if (!response.ok) {
            throw new Error(`Xbox profile request failed: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error getting Xbox profile:', error);
        throw error;
    }
};

// Link Xbox account to user
const linkXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('LinkXboxAccount: Received event body:', event.body);

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        let parsedBody: XboxLinkRequest;
        try {
            parsedBody = JSON.parse(event.body);
        } catch (parseError) {
            console.error('LinkXboxAccount: JSON parse error:', parseError);
            return createResponse(400, { error: 'Invalid JSON in request body' });
        }

        const { authCode } = parsedBody;

        if (!authCode) {
            return createResponse(400, {
                error: 'Missing required field: authCode'
            });
        }

        try {
            // Get Azure configuration
            const config = await getAzureConfig();
            if (!config) {
                return createResponse(500, { error: 'Azure configuration not available' });
            }

            // Exchange authorization code for Microsoft access token
            const tokenResponse = await fetch(MICROSOFT_TOKEN_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: new URLSearchParams({
                    client_id: config.clientId,
                    client_secret: config.clientSecret,
                    code: authCode,
                    grant_type: 'authorization_code',
                    redirect_uri: `https://${event.headers.Host}/xbox/callback`
                })
            });

            if (!tokenResponse.ok) {
                throw new Error(`Failed to exchange authorization code: ${tokenResponse.status}`);
            }

            const tokenData: MicrosoftTokenResponse = await tokenResponse.json();

            // Get Xbox Live tokens using Microsoft access token
            const xboxTokens = await getXboxLiveTokens(tokenData.access_token);

            // Get Xbox profile
            const xboxProfile = await getXboxProfile(xboxTokens.xstsToken, xboxTokens.userHash);

            // Extract gamertag from profile
            const gamertag = xboxProfile.profileUsers[0].settings.find((s: any) => s.id === 'Gamertag')?.value;
            if (!gamertag) {
                throw new Error('Could not retrieve gamertag from Xbox profile');
            }

            // Extract Xbox User ID (XUID)
            const xuid = xboxProfile.profileUsers[0].id;

            // Check if Xbox account is already linked to another user
            const existingXboxAuth = await checkExistingXboxAuth(xuid);
            if (existingXboxAuth && existingXboxAuth.userAuth.userId !== userId) {
                return createResponse(409, {
                    error: 'This Xbox account is already linked to another user'
                });
            }

            // Create Xbox authentication record
            await userAuthService.createUserAuth({
                userId,
                provider: AuthProvider.XBOX,
                providerUserId: xuid,
                username: gamertag, // Store Xbox gamertag as username
                refreshToken: tokenData.refresh_token,
                tokenExpiresAt: xboxTokens.expiresAt,
                providerData: {
                    xstsToken: xboxTokens.xstsToken,
                    userHash: xboxTokens.userHash,
                    gamertag: gamertag,
                    profilePictureUrl: xboxProfile.profileUsers[0].settings.find((s: any) => s.id === 'GameDisplayPicRaw')?.value
                },
                isPrimary: false, // Xbox is typically not primary auth
                isVerified: true
            });

            // Log Xbox account linking
            try {
                const { ipAddress, userAgent } = extractRequestMetadata(event);
                await logAccountLinkedXbox(userId, xuid, gamertag, ipAddress, userAgent);
            } catch (historyError) {
                console.error('Failed to log Xbox account linking:', historyError);
                // Don't fail the request if history logging fails
            }

            return createResponse(200, {
                message: 'Xbox account linked successfully',
                xboxAccount: {
                    id: xuid,
                    gamertag: gamertag,
                    profilePictureUrl: xboxProfile.profileUsers[0].settings.find((s: any) => s.id === 'GameDisplayPicRaw')?.value
                }
            });

        } catch (error) {
            console.error('LinkXboxAccount: Error:', error);
            return createResponse(500, {
                error: 'Failed to link Xbox account',
                details: (error as Error).message
            });
        }
    } catch (error) {
        console.error('LinkXboxAccount error:', error);
        return createResponse(500, {
            error: 'Failed to link Xbox account',
            details: (error as Error).message
        });
    }
};





// Helper function to check existing Xbox authentication (active only)
const checkExistingXboxAuth = async (xuid: string): Promise<any | null> => {
    try {
        const userAuth = await userAuthService.findUserAuthByProvider({
            provider: AuthProvider.XBOX,
            providerUserId: xuid
        });

        if (userAuth) {
            // Get the user record
            const getUserCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userAuth.userId }
            });
            const userResult = await dynamodb.send(getUserCommand);

            if (userResult.Item) {
                return {
                    userAuth,
                    user: userResult.Item as UserRecord
                };
            }
        }

        return null;
    } catch (error) {
        console.error('Error checking existing Xbox authentication:', error);
        return null;
    }
};

// Helper function to check for inactive Xbox authentication (previously unlinked)
const checkInactiveXboxAuth = async (xuid: string): Promise<any | null> => {
    try {
        // Look for inactive Xbox auth
        const userAuths = await userAuthService.findUserAuthsByUserId({ userId: '', activeOnly: false });

        // Find inactive Xbox auth with this XUID
        for (const userAuth of userAuths) {
            if (userAuth.provider === AuthProvider.XBOX &&
                userAuth.providerUserId === xuid &&
                !userAuth.isActive) {

                // Get the user record
                const getUserCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: userAuth.userId }
                });
                const userResult = await dynamodb.send(getUserCommand);

                if (userResult.Item) {
                    return {
                        userAuth,
                        user: userResult.Item as UserRecord
                    };
                }
            }
        }

        return null;
    } catch (error) {
        console.error('Error checking inactive Xbox authentication:', error);
        return null;
    }
};

// Helper function to get user's Xbox account
const getUserXboxAccount = async (userId: string): Promise<XboxAccount | null> => {
    try {
        const queryParams = {
            TableName: XBOX_ACCOUNTS_TABLE,
            IndexName: 'UserIdIndex', // GSI on userId
            KeyConditionExpression: 'userId = :userId',
            FilterExpression: 'isActive = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            }
        };

        const result = await dynamodb.send(new QueryCommand(queryParams));

        if (!result.Items || result.Items.length === 0) {
            return null;
        }

        return result.Items[0] as XboxAccount;
    } catch (error) {
        console.error('Error getting user Xbox account:', error);
        return null;
    }
};

// Unlink Xbox account
const unlinkXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // First, get the user's Xbox account to deactivate it
        const xboxAccount = await getUserXboxAccount(userId);
        if (xboxAccount) {
            // Deactivate the Xbox account record instead of deleting it
            const updateXboxParams = {
                TableName: XBOX_ACCOUNTS_TABLE,
                Key: { id: xboxAccount.id },
                UpdateExpression: 'SET isActive = :isActive, updatedAt = :updatedAt',
                ExpressionAttributeValues: {
                    ':isActive': false,
                    ':updatedAt': new Date().toISOString()
                }
            };
            await dynamodb.send(new UpdateCommand(updateXboxParams));
            console.log('Deactivated Xbox account record:', xboxAccount.id);
        }

        // Update user record to remove Xbox information
        const updateUserParams = {
            TableName: USERS_TABLE,
            Key: { id: userId },
            UpdateExpression: 'REMOVE xboxUserId, xboxGamertag SET updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':updatedAt': new Date().toISOString()
            }
        };
        await dynamodb.send(new UpdateCommand(updateUserParams));

        return createResponse(200, {
            message: 'Xbox account unlinked successfully'
        });

    } catch (error) {
        console.error('UnlinkXboxAccount error:', error);
        return createResponse(500, {
            error: 'Failed to unlink Xbox account',
            details: (error as Error).message
        });
    }
};

// Xbox Sign In - authenticate with Xbox account (create new account or login to existing)
const xboxSignIn = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('XboxSignIn: Received event body:', event.body);

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        let parsedBody: XboxSignInRequest;
        try {
            parsedBody = JSON.parse(event.body);
        } catch (parseError) {
            console.error('XboxSignIn: JSON parse error:', parseError);
            return createResponse(400, { error: 'Invalid JSON in request body' });
        }

        const { authCode } = parsedBody;

        if (!authCode) {
            return createResponse(400, { error: 'Authorization code is required' });
        }

        // Get Azure configuration
        const config = await getAzureConfig();
        if (!config) {
            return createResponse(500, { error: 'Xbox configuration not available' });
        }

        // Exchange authorization code for Microsoft access token
        const tokenParams = new URLSearchParams({
            client_id: config.clientId,
            client_secret: config.clientSecret,
            code: authCode,
            grant_type: 'authorization_code',
            redirect_uri: `https://${event.headers.Host}/xbox/callback`
        });

        const tokenResponse = await fetch(MICROSOFT_TOKEN_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: tokenParams.toString()
        });

        if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            console.error('XboxSignIn: Microsoft token exchange failed:', errorText);
            return createResponse(400, { error: 'Failed to exchange authorization code for access token' });
        }

        const tokenData: MicrosoftTokenResponse = await tokenResponse.json();

        // Get Xbox Live tokens using Microsoft access token
        const xboxTokens = await getXboxLiveTokens(tokenData.access_token);

        // Get Xbox profile
        const xboxProfile = await getXboxProfile(xboxTokens.xstsToken, xboxTokens.userHash);

        // Extract Xbox user information
        const gamertag = xboxProfile.profileUsers[0].settings.find((s: any) => s.id === 'Gamertag')?.value;
        if (!gamertag) {
            throw new Error('Could not retrieve gamertag from Xbox profile');
        }

        const xuid = xboxProfile.profileUsers[0].id;
        if (!xuid) {
            throw new Error('Could not retrieve Xbox User ID from profile');
        }

        // Check if Xbox authentication already exists (active)
        let existingXboxAuth = await checkExistingXboxAuth(xuid);

        if (existingXboxAuth) {
            // Xbox auth exists and is active - user record should exist
            console.log('XboxSignIn: Existing active Xbox authentication found, verifying user:', existingXboxAuth.userAuth.userId);

            if (!existingXboxAuth.user) {
                // User record is missing but Xbox auth exists - this is a data inconsistency
                // We'll delete the orphaned Xbox auth and treat this as a new user signup
                console.log('XboxSignIn: User record missing for Xbox auth, cleaning up and treating as new user');

                // Delete the orphaned Xbox auth record
                await userAuthService.deleteUserAuth(existingXboxAuth.userAuth.id);

                // Set to null so we continue with new user creation flow
                existingXboxAuth = null;
                console.log('XboxSignIn: Orphaned Xbox auth deleted, proceeding with new user creation');
            } else {
                // User record exists - proceed with normal sign-in
                const user = existingXboxAuth.user;

                // Update Xbox authentication tokens
                await userAuthService.updateUserAuth({
                    id: existingXboxAuth.userAuth.id,
                    username: gamertag, // Update gamertag in case it changed
                    refreshToken: tokenData.refresh_token,
                    tokenExpiresAt: xboxTokens.expiresAt,
                    providerData: {
                        ...existingXboxAuth.userAuth.providerData,
                        xstsToken: xboxTokens.xstsToken,
                        userHash: xboxTokens.userHash,
                        gamertag: gamertag
                    },
                    lastUsedAt: new Date().toISOString()
                });

                // Generate auth tokens for the user (bypasses Cognito)
                const authResult = await generateAuthTokensWithContext(user.id);

                if (!authResult) {
                    return createResponse(500, { error: 'Failed to generate authentication tokens' });
                }

                // Check if username is required
                const requiresUsername = !user.username || user.username.trim() === '';

                // Log Xbox sign in for existing user
                try {
                    const { ipAddress, userAgent } = extractRequestMetadata(event);
                    await logXboxSignIn(user.id, xuid, gamertag, ipAddress, userAgent);
                } catch (historyError) {
                    console.error('Failed to log Xbox sign in:', historyError);
                    // Don't fail the request if history logging fails
                }

                return createResponse(200, {
                    message: 'Xbox Sign In successful',
                    user: {
                        id: user.id,
                        email: user.email,
                        username: user.username,
                        firstName: user.firstName,
                        lastName: user.lastName,
                        createdAt: user.createdAt,
                        updatedAt: user.updatedAt
                    },
                    tokens: {
                        accessToken: authResult.accessToken,
                        refreshToken: authResult.refreshToken,
                        idToken: authResult.idToken
                    },
                    requiresUsername
                });
            }
        }

        // If we reach here, either no Xbox auth exists or we cleaned up an orphaned one
        // Check if there's an inactive Xbox auth (previously unlinked)
        if (!existingXboxAuth) {
            const inactiveXboxAuth = await checkInactiveXboxAuth(xuid);

            if (inactiveXboxAuth) {
                // Xbox account was previously linked but unlinked - provide options
                console.log('XboxSignIn: Inactive Xbox account found, providing account options');

                // Get the user record for the inactive account
                const getUserCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: inactiveXboxAuth.userId }
                });
                const userResult = await dynamodb.send(getUserCommand);

                if (userResult.Item) {
                    const user = userResult.Item as UserRecord;

                    return createResponse(409, {
                        message: 'Xbox account was previously linked to an existing account',
                        accountOptions: {
                            existingAccount: {
                                userId: user.id,
                                username: user.username,
                                email: user.email,
                                firstName: user.firstName,
                                lastName: user.lastName
                            },
                            canRelink: true,
                            canCreateNew: true
                        },
                        xboxData: {
                            xuid: xuid,
                            gamertag: gamertag,
                            xstsToken: xboxTokens.xstsToken,
                            userHash: xboxTokens.userHash,
                            tokenExpiresAt: xboxTokens.expiresAt,
                            refreshToken: tokenData.refresh_token
                        }
                    });
                }
            }

            // Xbox account doesn't exist - create new user account
            console.log('XboxSignIn: No existing Xbox account found, creating new user account');

            // Create a new user account with Xbox information
            const userId = uuidv4();
            const now = new Date().toISOString();

            // Generate a unique email for Xbox users (they can change it later)
            const xboxEmail = `xbox_${xuid}@gameflex.temp`;

            // Check if there's an inactive Xbox account for this XUID that can be reactivated
            const inactiveAccount = await checkInactiveXboxAuth(xuid);
            if (inactiveAccount) {
                // Reactivate the existing Xbox account
                const updateCommand = new UpdateCommand({
                    TableName: XBOX_ACCOUNTS_TABLE,
                    Key: { id: inactiveAccount.id },
                    UpdateExpression: 'SET isActive = :active, xstsToken = :token, userHash = :hash, microsoftRefreshToken = :refreshToken, updatedAt = :updatedAt, tokenExpiresAt = :expiresAt',
                    ExpressionAttributeValues: {
                        ':active': true,
                        ':token': xboxTokens.xstsToken,
                        ':hash': xboxTokens.userHash,
                        ':refreshToken': tokenData.refresh_token,
                        ':updatedAt': now,
                        ':expiresAt': xboxTokens.expiresAt
                    }
                });
                await dynamodb.send(updateCommand);

                // Get the existing user record
                const getUserCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: inactiveAccount.userId }
                });
                const userResult = await dynamodb.send(getUserCommand);

                if (!userResult.Item) {
                    return createResponse(404, { error: 'User account not found for reactivated Xbox account' });
                }

                const user = userResult.Item as UserRecord;

                // Generate JWT tokens for the existing user (bypasses Cognito)
                const authResult = await generateAuthTokensWithContext(user.id);

                return createResponse(200, {
                    message: 'Xbox Sign In successful - account reactivated',
                    user: {
                        id: user.id,
                        email: user.email,
                        username: user.username || null,
                        firstName: user.firstName,
                        lastName: user.lastName,
                        createdAt: user.createdAt,
                        updatedAt: user.updatedAt
                    },
                    tokens: {
                        accessToken: authResult.accessToken,
                        refreshToken: authResult.refreshToken,
                        idToken: authResult.idToken
                    },
                    requiresUsername: !user.username
                });
            }

            // Create user record in DynamoDB with Xbox gamertag as display name
            const userRecord: UserRecord = {
                id: userId,
                email: xboxEmail,
                firstName: gamertag,
                lastName: '',
                displayName: gamertag,
                displayNameProvider: XBOX_PROVIDER,
                cognitoUserId: undefined, // Xbox users don't use Cognito
                createdAt: now,
                updatedAt: now
            };

            const putUserCommand = new PutCommand({
                TableName: USERS_TABLE,
                Item: userRecord
            });
            await dynamodb.send(putUserCommand);

            // Create Xbox account record
            const xboxAccountId = uuidv4();
            const xboxAccount: XboxAccount = {
                id: xboxAccountId,
                userId,
                xboxUserId: xuid,
                gamertag: gamertag,
                xstsToken: xboxTokens.xstsToken,
                userHash: xboxTokens.userHash,
                microsoftRefreshToken: tokenData.refresh_token, // Store refresh token for automatic renewal
                profilePictureUrl: xboxProfile.profileUsers[0].settings.find((s: any) => s.id === 'GameDisplayPicRaw')?.value,
                isActive: true,
                createdAt: now,
                updatedAt: now,
                tokenExpiresAt: xboxTokens.expiresAt
            };

            const putXboxCommand = new PutCommand({
                TableName: XBOX_ACCOUNTS_TABLE,
                Item: xboxAccount
            });
            await dynamodb.send(putXboxCommand);

            // Generate JWT tokens for the new user (bypasses Cognito)
            const authResult = await generateAuthTokensWithContext(userId);

            // Log Xbox sign up for new user
            try {
                const { ipAddress, userAgent } = extractRequestMetadata(event);
                await logXboxSignUp(userId, xuid, gamertag, ipAddress, userAgent);
            } catch (historyError) {
                console.error('Failed to log Xbox sign up:', historyError);
                // Don't fail the request if history logging fails
            }

            return createResponse(201, {
                message: 'Xbox Sign In successful - new account created',
                user: {
                    id: userId,
                    email: xboxEmail,
                    username: null, // Username will be required
                    firstName: gamertag,
                    lastName: '',
                    createdAt: now,
                    updatedAt: now
                },
                tokens: {
                    accessToken: authResult.accessToken,
                    refreshToken: authResult.refreshToken,
                    idToken: authResult.idToken
                },
                requiresUsername: true // New Xbox accounts always require username setup
            });
        }

        // Fallback - this should never be reached
        return createResponse(500, { error: 'Unexpected error in Xbox sign-in flow' });

    } catch (error) {
        console.error('XboxSignIn error:', error);
        return createResponse(500, {
            error: 'Failed to sign in with Xbox',
            details: (error as Error).message
        });
    }
};

// Get user's linked Xbox account
const getLinkedXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const queryParams = {
            TableName: XBOX_ACCOUNTS_TABLE,
            IndexName: 'UserIdIndex', // GSI on userId
            KeyConditionExpression: 'userId = :userId',
            FilterExpression: 'isActive = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            }
        };

        const result = await dynamodb.send(new QueryCommand(queryParams));

        if (!result.Items || result.Items.length === 0) {
            return createResponse(404, { error: 'No Xbox account linked' });
        }

        const xboxAccount = result.Items[0] as XboxAccount;

        return createResponse(200, {
            xboxAccount: {
                id: xboxAccount.id,
                gamertag: xboxAccount.gamertag,
                xboxUserId: xboxAccount.xboxUserId,
                profilePictureUrl: xboxAccount.profilePictureUrl,
                linkedAt: xboxAccount.createdAt
            }
        });

    } catch (error) {
        console.error('GetLinkedXboxAccount error:', error);
        return createResponse(500, {
            error: 'Failed to get Xbox account',
            details: (error as Error).message
        });
    }
};

// Handler for starting Xbox authentication flow
const startXboxAuth = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        // For Xbox sign in, userId can be null (unauthenticated users)
        // For Xbox linking, userId is required (authenticated users)

        const config = await getAzureConfig();

        if (!config) {
            return createResponse(500, { error: 'Azure configuration not available' });
        }

        if (!config.clientId) {
            return createResponse(500, { error: 'Azure client ID not configured' });
        }

        // Generate the Microsoft OAuth URL
        const redirectUri = `https://${event.headers.Host}/xbox/callback`;
        const scopes = 'XboxLive.signin XboxLive.offline_access';

        // Use userId as state if available (for linking), otherwise use 'signin' (for sign in)
        const state = userId || 'signin';

        const authUrl = `${MICROSOFT_AUTH_URL}?` + new URLSearchParams({
            client_id: config.clientId,
            response_type: 'code',
            redirect_uri: redirectUri,
            scope: scopes,
            state: state
        }).toString();

        return createResponse(200, {
            authUrl,
            message: 'Redirect user to this URL to authenticate with Xbox Live'
        });
    } catch (error) {
        console.error('StartXboxAuth error:', error);
        return createResponse(500, {
            error: 'Failed to start Xbox authentication',
            details: (error as Error).message
        });
    }
};

// Handler for Microsoft OAuth callback
const handleXboxCallback = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('Xbox callback received:', JSON.stringify(event.queryStringParameters, null, 2));

        const { code, state } = event.queryStringParameters || {};

        if (!code) {
            return createResponse(400, { error: 'Authorization code is required' });
        }

        if (!state) {
            return createResponse(400, { error: 'State parameter (user ID) is required' });
        }

        // Return a simple success page that will trigger the frontend to complete the linking
        const successHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Xbox Account Linking</title>
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .success { color: #4CAF50; }
                    .loading { color: #2196F3; }
                </style>
            </head>
            <body>
                <h1 class="success">✓ Xbox Authentication Successful</h1>
                <p class="loading">Completing account linking...</p>
                <script>
                    // Post message to parent window (mobile app webview)
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'xbox_auth_success',
                            code: '${code}',
                            state: '${state}'
                        }));
                    }
                    // For web browsers, redirect to a custom URL scheme
                    else {
                        window.location.href = 'io.gameflex.oauth://xbox/callback?code=${encodeURIComponent(code)}&state=${encodeURIComponent(state)}';
                    }
                </script>
            </body>
            </html>
        `;

        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'text/html',
                'Access-Control-Allow-Origin': '*'
            },
            body: successHtml
        };

    } catch (error) {
        console.error('HandleXboxCallback error:', error);
        return createResponse(500, {
            error: 'Failed to handle Xbox callback',
            details: (error as Error).message
        });
    }
};



// Relink Xbox account to existing user (after unlinking)
const relinkXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { xboxData, userId } = JSON.parse(event.body);

        if (!xboxData || !userId) {
            return createResponse(400, { error: 'Xbox data and user ID are required' });
        }

        // Verify the user exists
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });
        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User account not found' });
        }

        const user = userResult.Item as UserRecord;

        // Find the inactive Xbox account and reactivate it
        const inactiveXboxAccount = await checkInactiveXboxAuth(xboxData.xuid);

        if (!inactiveXboxAccount || inactiveXboxAccount.userId !== userId) {
            return createResponse(404, { error: 'Inactive Xbox account not found for this user' });
        }

        // Reactivate and update the Xbox account
        const updateXboxParams = {
            TableName: XBOX_ACCOUNTS_TABLE,
            Key: { id: inactiveXboxAccount.id },
            UpdateExpression: 'SET isActive = :isActive, xstsToken = :xstsToken, userHash = :userHash, tokenExpiresAt = :tokenExpiresAt, microsoftRefreshToken = :refreshToken, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':isActive': true,
                ':xstsToken': xboxData.xstsToken,
                ':userHash': xboxData.userHash,
                ':tokenExpiresAt': xboxData.tokenExpiresAt,
                ':refreshToken': xboxData.refreshToken,
                ':updatedAt': new Date().toISOString()
            }
        };
        await dynamodb.send(new UpdateCommand(updateXboxParams));

        // Generate JWT tokens for the user (bypasses Cognito)
        const authResult = await generateAuthTokensWithContext(user.id);

        // Check if username is required
        const requiresUsername = !user.username || user.username.trim() === '';

        return createResponse(200, {
            message: 'Xbox account relinked successfully',
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            },
            tokens: {
                accessToken: authResult.accessToken,
                refreshToken: authResult.refreshToken,
                idToken: authResult.idToken
            },
            requiresUsername
        });

    } catch (error) {
        console.error('RelinkXboxAccount error:', error);
        return createResponse(500, {
            error: 'Failed to relink Xbox account',
            details: (error as Error).message
        });
    }
};

// Create new Xbox account (ignoring previous link)
const createNewXboxAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { xboxData } = JSON.parse(event.body);

        if (!xboxData) {
            return createResponse(400, { error: 'Xbox data is required' });
        }

        // Create a new user account with Xbox information
        const userId = uuidv4();
        const now = new Date().toISOString();

        // Generate a unique email for Xbox users (they can change it later)
        const xboxEmail = `xbox_${xboxData.xuid}@gameflex.temp`;

        // Create user record in DynamoDB with Xbox gamertag as display name (bypasses Cognito)
        const userRecord: UserRecord = {
            id: userId,
            email: xboxEmail,
            firstName: xboxData.gamertag,
            lastName: '',
            displayName: xboxData.gamertag,
            displayNameProvider: XBOX_PROVIDER,
            cognitoUserId: undefined, // Xbox users don't use Cognito
            createdAt: now,
            updatedAt: now
        };

        const putUserCommand = new PutCommand({
            TableName: USERS_TABLE,
            Item: userRecord
        });
        await dynamodb.send(putUserCommand);

        // Create Xbox account record
        const xboxAccountRecord: XboxAccount = {
            id: uuidv4(),
            userId: userId,
            xboxUserId: xboxData.xuid,
            gamertag: xboxData.gamertag,
            xstsToken: xboxData.xstsToken,
            userHash: xboxData.userHash,
            microsoftRefreshToken: xboxData.refreshToken,
            profilePictureUrl: undefined, // Will be fetched later
            isActive: true,
            createdAt: now,
            updatedAt: now,
            tokenExpiresAt: xboxData.tokenExpiresAt
        };

        const putXboxCommand = new PutCommand({
            TableName: XBOX_ACCOUNTS_TABLE,
            Item: xboxAccountRecord
        });
        await dynamodb.send(putXboxCommand);

        // Generate JWT tokens for the new user (bypasses Cognito)
        const authResult = await generateAuthTokensWithContext(userId);

        return createResponse(201, {
            message: 'New Xbox account created successfully',
            user: {
                id: userId,
                email: xboxEmail,
                firstName: xboxData.gamertag,
                lastName: '',
                createdAt: now,
                updatedAt: now
            },
            tokens: {
                accessToken: authResult.accessToken,
                refreshToken: authResult.refreshToken,
                idToken: authResult.idToken
            },
            requiresUsername: true // New Xbox accounts always require username setup
        });

    } catch (error) {
        console.error('CreateNewXboxAccount error:', error);
        return createResponse(500, {
            error: 'Failed to create new Xbox account',
            details: (error as Error).message
        });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Xbox handler received event:', JSON.stringify(event, null, 2));

    const httpMethod = event.httpMethod;
    const resource = event.resource;

    try {
        if (httpMethod === 'GET' && resource === '/xbox/auth') {
            return await startXboxAuth(event);
        } else if (httpMethod === 'GET' && resource === '/xbox/callback') {
            return await handleXboxCallback(event);
        } else if (httpMethod === 'POST' && resource === '/xbox/link') {
            return await linkXboxAccount(event);
        } else if (httpMethod === 'POST' && resource === '/xbox/signin') {
            return await xboxSignIn(event);
        } else if (httpMethod === 'GET' && resource === '/xbox/account') {
            return await getLinkedXboxAccount(event);
        } else if (httpMethod === 'DELETE' && resource === '/xbox/account') {
            return await unlinkXboxAccount(event);
        } else if (httpMethod === 'POST' && resource === '/xbox/relink-account') {
            return await relinkXboxAccount(event);
        } else if (httpMethod === 'POST' && resource === '/xbox/create-new-account') {
            return await createNewXboxAccount(event);
        } else {
            return createResponse(404, { error: 'Endpoint not found' });
        }
    } catch (error) {
        console.error('Xbox handler error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: (error as Error).message
        });
    }
};
