/**
 * UserAuth Types and Interfaces
 * 
 * This file defines the schema for the centralized UserAuths table
 * that handles all authentication methods (email/password, Xbox, Apple, etc.)
 */

export enum AuthProvider {
    EMAIL = 'email',
    XBOX = 'xbox',
    APPLE = 'apple',
    TWITCH = 'twitch',
    KICK = 'kick',
    GOOGLE = 'google',
    DISCORD = 'discord'
}

export interface UserAuth {
    // Primary identifiers
    id: string;                          // Partition key (UUID)
    userId: string;                      // Foreign key to Users table
    provider: AuthProvider;              // Authentication provider type

    // Provider-specific identifiers
    providerUserId?: string;             // Provider's user ID (e.g., Xbox XUID, Apple user ID)
    email?: string;                      // Email address (for email auth or if provided by provider)
    username?: string;                   // Username from provider (e.g., Xbox gamertag, Twitch username)

    // Authentication tokens and credentials
    accessToken?: string;                // Provider access token (encrypted)
    refreshToken?: string;               // Provider refresh token (encrypted)
    idToken?: string;                    // Provider ID token (encrypted)
    tokenExpiresAt?: string;             // Token expiration timestamp (ISO string)

    // Provider-specific data (generic fields to accommodate different providers)
    providerData?: {
        // Xbox-specific fields
        xstsToken?: string;              // Xbox XSTS token
        userHash?: string;               // Xbox user hash
        gamertag?: string;               // Xbox gamertag

        // Apple-specific fields
        firstName?: string;              // Apple provided first name
        lastName?: string;               // Apple provided last name

        // Twitch-specific fields
        twitchUsername?: string;         // Twitch username

        // Generic fields for any provider
        displayName?: string;            // Provider display name
        profilePictureUrl?: string;      // Provider profile picture
        [key: string]: any;              // Allow additional provider-specific data
    };

    // Status and metadata
    isActive: boolean;                   // Whether this auth method is active
    isPrimary: boolean;                  // Whether this is the primary auth method for the user
    isVerified: boolean;                 // Whether this auth method is verified
    lastUsedAt?: string;                 // Last time this auth method was used (ISO string)

    // Timestamps
    createdAt: string;                   // Creation timestamp (ISO string)
    updatedAt: string;                   // Last update timestamp (ISO string)
}

/**
 * Request interfaces for authentication operations
 */
export interface CreateUserAuthRequest {
    userId: string;
    provider: AuthProvider;
    providerUserId?: string;
    email?: string;
    username?: string;
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    tokenExpiresAt?: string;
    providerData?: UserAuth['providerData'];
    isPrimary?: boolean;
    isVerified?: boolean;
}

export interface UpdateUserAuthRequest {
    id: string;
    username?: string;
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    tokenExpiresAt?: string;
    providerData?: UserAuth['providerData'];
    isActive?: boolean;
    isPrimary?: boolean;
    isVerified?: boolean;
    lastUsedAt?: string;
}

/**
 * Response interfaces for authentication operations
 */
export interface UserAuthResponse {
    userAuth: UserAuth;
    user?: {
        id: string;
        email?: string;
        username?: string;
        firstName?: string;
        lastName?: string;
        displayName?: string;
    };
}

/**
 * Query interfaces for authentication lookups
 */
export interface FindUserAuthByProviderRequest {
    provider: AuthProvider;
    providerUserId?: string;
    email?: string;
}

export interface FindUserAuthsByUserIdRequest {
    userId: string;
    activeOnly?: boolean;
}

/**
 * Migration interfaces for existing data
 */
export interface MigrateEmailAuthRequest {
    userId: string;
    email: string;
    cognitoUserId: string;
}

export interface MigrateXboxAuthRequest {
    userId: string;
    xboxUserId: string;
    gamertag: string;
    xstsToken: string;
    userHash: string;
    microsoftRefreshToken?: string;
    profilePictureUrl?: string;
    tokenExpiresAt: string;
}

export interface MigrateAppleAuthRequest {
    userId: string;
    appleUserId: string;
    email?: string;
    firstName?: string;
    lastName?: string;
}

/**
 * Validation helpers
 */
export const isValidAuthProvider = (provider: string): provider is AuthProvider => {
    return Object.values(AuthProvider).includes(provider as AuthProvider);
};

export const getProviderDisplayName = (provider: AuthProvider): string => {
    const displayNames: Record<AuthProvider, string> = {
        [AuthProvider.EMAIL]: 'Email',
        [AuthProvider.XBOX]: 'Xbox',
        [AuthProvider.APPLE]: 'Apple',
        [AuthProvider.TWITCH]: 'Twitch',
        [AuthProvider.KICK]: 'Kick',
        [AuthProvider.GOOGLE]: 'Google',
        [AuthProvider.DISCORD]: 'Discord'
    };
    return displayNames[provider];
};

/**
 * Constants for table operations
 */
export const USER_AUTH_TABLE_INDEXES = {
    USER_ID_INDEX: 'UserIdIndex',
    PROVIDER_USER_ID_INDEX: 'ProviderUserIdIndex',
    EMAIL_INDEX: 'EmailIndex',
    PROVIDER_INDEX: 'ProviderIndex',
    USERNAME_INDEX: 'UsernameIndex'
} as const;

export const USER_AUTH_QUERY_LIMITS = {
    DEFAULT_LIMIT: 50,
    MAX_LIMIT: 100
} as const;
